 # [59-螺旋矩阵 II ](https://leetcode.cn/problems/spiral-matrix-ii/description/)
## cpp基础

1. 二维数组初始化
	-  vector<vector<int>> v(n,vector<int>(n));
2. 隐式类型转换（舍弃小数）
	- `int/2`此时整体为`int`类型，会直接将小数舍弃。
		1. +0.5可四舍五入。
		2. `int/2.0`此时为浮点数，小数可保留。
# 重点

1. 递归结构，`n`阶是`n-2`阶整体平移，再外嵌一层得到的。
2. 搞清各个位置数与`n`,`层数`,`位置` 表达式。

## 编程bug

1. 数组从0开始

## 算法bug

1. 位置关系循环填数的时候要注意，`i`的起始值。
   > - `for(int j=1;j<n-2*i-1;j++)`，此处`j`是从`1`开始的。
   

## 完整代码：

```cpp
 vector<vector<int>> generateMatrix(int n) {
        int i=0;
        //二维数组
        vector<vector<int>> v(n,vector<int>(n));
        int other=0;
        //隐式类型转换
        while(i<(n/2.0))
        {
            for(int j=0;j<(n-2*i);j++)
            {
                v[i][j+i]=j+1+other;
                //从0开始
                v[n-1-i][j+i]=3*(n-2*i)-2-j+other;
            }
            //初始值
            //for循环执行次序
            for(int j=1;j<n-2*i-1;j++)
            {
                v[j+i][i]=4*(n-2*i-1)-j+1+other;
                v[j+i][n-1-i]=(n-2*i)+j+other;
            }
            other+=4*(n-1-2*i);
            i++;
        }
        return v;
    }
```