# Coding
刷leetcode的记录，聊以自慰。
>使用的是leedcode配合某刷题单。
>积累cpp语法是通过[cpp参考站](https://legacy.cplusplus.com/reference/)。
1. 数组
	
	- 704-[二分查找](./数组/704-二分查找.md)（2.8）
	- 27-[移除元素](./数组/27-移除元素.md)（2.9）
	- 977-[有序数组的平方](./数组/977-有序数组的平方.md)(2.10)
	- 209-[长度最小的子数组](./数组/209-长度最小的子数组.md)(2.11)
	- 58-[区间和](./数组/58-区间和.md)(2.17)
	- 59-[螺旋矩阵II](./数组/59-螺旋矩阵II.md)(2.17)
	- 44 - [开发商购买土地](./数组/44-开发商购买土地.md)(2.19)
	- 数组方法总结
	  - 双指针
	  - 二分法
	  - 前缀和
	  - 滑动窗口

2. 链表
	- 203-[移除链表元素](./List/203%20移除链表元素.md)(2.25)
	- 

