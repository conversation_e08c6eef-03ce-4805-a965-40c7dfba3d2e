# [27-移除元素](https://leetcode.cn/problems/remove-element/)

## 算法bug
1. 输入检测，导致越界。
   ```cpp
   if(nums.empty()) return -1;
   ```
2. 标志位刷新！！！

## 完整代码：

```cpp
int removeElement(vector<int>& nums, int val) {
        int l=0,r=(nums.size()-1);
        if(r<0)
            return 0;
        bool flagl=false,flagr=false;
        //针对当前逻辑应为<
        while(l<r)
        {
        	//每个循环要重新刷新
            flagl=false;
            flagr=false;
            if (nums[l]!=val)
                l++;
            else    
                flagl=true;
            if (nums[r]==val)
                r--;
            else    
                flagr=true;
            if(flagl&&flagr)
            {
            	//位操作实现互换
                nums[l]^=nums[r];
                nums[r]^=nums[l];
                nums[l]^=nums[r];
            }
        }
        if(nums[l]!=val)
            l++;
        return l;
    }
```