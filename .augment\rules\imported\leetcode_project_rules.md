---
type: "always_apply"
---

# LeetCode刷题项目工作规则

## 项目概述
- **项目名称**: LeetCode算法题解记录
- **主要功能**: 算法学习、代码调试、题解归档
- **文件结构**: 按算法专题分类管理

## 工作流程规则

### 1. 日常咨询和Debug
- **响应方式**: 简洁明了，重点突出
- **代码示例**: 使用`<augment_code_snippet>`标签
- **解释重点**: 算法思路、常见bug、C++语法细节

### 2. 代码归档流程

#### 2.1 信息收集
- 确认算法专题和LeetCode题号、题目名称
- 提取用户注释中的核心思路和遇到的错误

#### 2.2 文件创建规则
- **路径格式**: `Coding\code\{专题名称}\{题号} {题目名称}.cpp`
- **同一题目**: 放在同一文件，用注释区分不同算法

#### 2.3 代码格式要求
```cpp
// {题目名称}
// 关键点: {核心思路}
// 易错点: {用户遇到的错误}
// 日期: {YYYY.MM.DD}

# include <头文件>
using namespace std;

//算法1: {算法名称}
{代码1}

//算法2: {算法名称} 
{代码2}
```

### 3. 专题文件夹管理
- **现有专题**: 二分查找、双指针、滑动窗口、数组等
- **新专题**: 根据题目类型自动创建

## 响应模板

### 代码归档确认
```
收到代码，准备归档：
- 专题：{专题名称}
- 题号：{题号}  
- 题目：{题目名称}
- 文件路径：code\{专题}\{题号} {题目}.cpp
```

## 注意事项
1. 注释精简但包含关键信息
2. 重点记录用户的错误和思路
3. 同一题目多种解法放同一文件
4. 日期格式：YYYY.MM.DD

## 更新记录
- 创建日期: 2024.12.19
- 版本: v1.0
- 更新: 简化版本管理，精简注释格式