 # [203-移除链表元素](https://leetcode.cn/problems/remove-linked-list-elements/submissions/603486968/)
 
## 编程bug

1. 定义连续指针,要在多变量前加`*`.
```cpp
 //连续指针定义
        ListNode * temp=head,*bef=head;
```

## 算法优化

1. 边界逻辑处理
   用伪节点更好,可以避免意想不到的情况.
   ```cpp
    //边界还是引入虚拟节点较好.
        while(head->val==val){
            if(head->next)
                head=head->next;
            else   
                return nullptr;
        }
   ```
   原来只考虑到了首节点是目标值的情况,但是没考虑到前几个节点均为目标值的情况.

## 完整代码：

```cpp
  /*
 * Definition for singly-linked list.
 * struct ListNode {
 *     int val;
 *     ListNode *next;
 *     ListNode() : val(0), next(nullptr) {}
 *     ListNode(int x) : val(x), next(nullptr) {}
 *     ListNode(int x, ListNode *next) : val(x), next(next) {}
 * };
 */
class Solution {
public:
    ListNode* removeElements(ListNode* head, int val) {
        //连续指针定义
        ListNode * temp=head,*bef=head;
        if(!head)
            return head;
        //边界还是引入虚拟节点较好.
        while(head->val==val){
            if(head->next)
                head=head->next;
            else   
                return nullptr;
        }
        while(bef->next){
            if(temp->val==val)
                bef->next=temp->next;
            else
                bef=temp;
            temp=temp->next;
        }
        return head;
    }
};
```