 # [977-有序数组的平方](https://leetcode.cn/problems/squares-of-a-sorted-array/)
## cpp基础

1. [std::vector::vector()](https://legacy.cplusplus.com/reference/vector/vector/vector/)
1. [std::power(Type1 base ,Type2 exponent)](https://legacy.cplusplus.com/reference/cmath/pow/)

## 关键

1. 找到正负分界线：平方后的数组在分界线左右，为两个单调数组，那么问题转化为将两个单调数组合并为一个单调数组。

## 完整代码：

```cpp
class Solution {
public:
    vector<int> sortedSquares(vector<int>& nums) {
        int i=0,len=nums.size();
        
        for(int k=0;k<len;k++)
        {
            if(nums[k]<0)
                i++;
            nums[k]=pow(nums[k],2);
        }
        int l=i-1;
        int r=i;
        int j=0; 
        vector<int> v(nums);   
        while(l>-1&&r<len)
        {
            if(nums[l]>nums[r])
            {
                v[j++]=nums[r];
                r++;
            }
            else
            {
                v[j++]=nums[l];
                l--;
            }
        }
        while(l>-1)
            v[j++]=nums[l--];
        return v;
    }
};
```