# [209-长度最小的子数组](https://leetcode.cn/problems/minimum-size-subarray-sum/)

## 关键
1. 滑动窗口的方法

## 算法bug
1. 涉及到指针更新(++/--)且在此次循环中访问对应更新后的位置，应特殊处理，防止非法越界。
```cpp
//越界判断
  if(r!=(len-1))
     sum+=nums[++r];`
```
2. 滑动窗口的结束条件与双指针有所区别：还要考虑到右边到头，但此时不符合条件的情况。

## 完整代码：

```cpp
   int minSubArrayLen(int target, vector<int>& nums) {
        int len=nums.size(),t=len+1;
        int l=0,r=0,sum=nums[0];
        //退出条件
        while(l<=r&&r<len)
        {
            if(sum<target)
            {
               //越界判断
                if(r!=(len-1))
                    sum+=nums[++r];
                else
                    break;
            }
            else{
                //r-l不是
                t=(r-l+1)<t?(r-l+1):t;
                sum-=nums[l++];
            }     
        }
        return t>len?0:t;
    }
```