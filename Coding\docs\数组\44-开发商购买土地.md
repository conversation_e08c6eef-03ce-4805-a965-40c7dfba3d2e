 # [44 - 开发商购买土地（第五期模拟笔试）](https://kamacoder.com/problempage.php?pid=1044)


## 关键点

1. 滑动窗口方法
## 编程bug

1. 头文件

   - `include <climits>`
   - `include <vector>`
2. 三目表达式
   - `min=min<=abs(sum-2*sum2)?min:abs(sum-2*sum2);`

## 算法bug

1. 循环更新
	- 当循环前有赋初值操作（如`min=arr[0]`），应先更新再操作。
	>```cpp
	>  while(l<m&&r<m){
    >    //给了初值，应先更新
    >   min=min<=abs(sum-2*sum2)?min:abs(sum-2*sum2);
    >    if((sum-2*sum2)>0)
    >       \\操作
    >    else    
    >     	\\操作
    > } 
    > ```
## 算法拓展
- 二维前缀和

## 完整代码：

```cpp
# include <iostream>
//头文件
# include <climits>
# include <vector>
using namespace std;
//这是两个竖线分割为两个区域，只能一个线分割两个区域
int main()
{
    int n,m,t,sum=0,sum1,min=INT_MAX;
    cin>>n>>m;
    if(n<=0||m<=0)
        return 0;
    vector<int>row;
    vector<int>col(m,0);
    for (int i=n-1;i>=0;i--) {
        sum1=0;
        for(int j=m-1;j>=0;j--)
            {
                cin>>t;
                sum1+=t;
                col[m-1-j]+=t;
            }
        sum+=sum1;
        row.push_back(sum1);
    }
    int sum2=row[0],l=0,r=0;
    while(l<n&&r<n){
        //三目表达式
        min=min<=abs(sum-2*sum2)?min:abs(sum-2*sum2);
        if((sum-2*sum2)>0)
        {
            if(r==(n-1))
                break;
            else
                sum2+=row[++r];
        }
        else
            break;
        //  sum2-=row[l++];
    }
    sum2=col[0];
    l=0;
    r=0;
    while(l<m&&r<m){
        //给了初值，应先更新
        min=min<=abs(sum-2*sum2)?min:abs(sum-2*sum2);
        if((sum-2*sum2)>0){
            if(r==(m-1))
                break;
            else
                sum2+=col[++r];
        }
           
        else 
            break;
            // sum2-=col[l++];
    }
    cout<<min;
    return 0;
}
```
## 拓展代码
``` cpp
//二维前缀和 
//输入，类似于概率分布函数的思想
     for (int i = 1; i <= n; i++)
         for (int j = 1; j <= m; j++)
         {
             int x;
             cin >> x;
             s[i][j] = s[i - 1][j] + s[i][j - 1] - s[i - 1][j - 1] + x;
         }
//固定一个变量，转化为一维前缀和
     for(int i = 1; i < n; i++)
     {
        int x = abs(s[n][m] - 2 * s[i][m]);
         ans = min(ans, x);
     }
     for(int i = 1; i < m; i++)
     {
         int x = abs(s[n][m] - 2 * s[n][i]);
         ans = min(ans, x);
     }
     cout << ans;
```