 # [58-区间和](https://kamacoder.com/problempage.php?pid=1070)
## cpp基础

1. [constructor(无初值）queue ()](https://legacy.cplusplus.com/reference/queue/queue/queue/)
> 若无初始值，构造函数如上，空间可自动调节。
2. [std::vector::push_back()](https://legacy.cplusplus.com/reference/vector/vector/push_back/)
> `vector`高效插入，后面。
## 重点

1. 输入`while((cin>>a>>b))`
	> cin有返回值，若输入失败，返回false 

## 算法bug
1. 输入变量
> - 不要直接对其操作，而是找中间值，以防后面用到时，出现意外错误。
>	>如本题的n，输入逻辑是通过`cin`计数，若改变了`n`，后面进行输入检测时`if((b<n)&&(a>-1)&&(b>=a))`，会发现错误输出。
2. 中间变量
> - 注意刷新逻辑。
> 	>如本题的`sum`应在每组输入前刷新。
## 完整代码：

```cpp
//iostream,头文件
# include <iostream>
# include <vector>
# include <queue>
using namespace std;
// main 返回值
int main()
{
    int a,b,n,sum=0;
    cin>>n;
    if(n<0)
        return -1;
    //constructer
    vector<int> Array ;
    queue<int> result ; 
    //输出的值最好不要直接进行操作--
    int i=n;
    while(i--)
    {
        int temp;
        cin>>temp;
        //vector 后增 https://legacy.cplusplus.com/reference/vector/vector/push_back/
        Array.push_back(temp);
    }
    //栈 ? 队列
    while((cin>>a>>b))
    {
        //重置
        if((b<n)&&(a>-1)&&(b>=a))
        {
        sum=0;
        int i=a;
        while(i<=b)
            sum+=Array[i++]; 
        result.push(sum);
        }
        else
            continue;
    }
    while(!result.empty())
    {
        int r;
        r=result.front();
        result.pop();
        cout<<r<<endl;
    }
    return 0;
}
```